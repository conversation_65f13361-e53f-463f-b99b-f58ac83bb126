import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import Constants from 'expo-constants';
import * as Location from 'expo-location';
import { colors, spacing } from '../constants/theme';

const { width, height } = Dimensions.get('window');

export const MapTestScreen: React.FC = () => {
  const [mapReady, setMapReady] = useState(false);
  const [mapError, setMapError] = useState<string | null>(null);
  const [location, setLocation] = useState<Location.LocationObject | null>(null);
  const [permissionStatus, setPermissionStatus] = useState<string>('unknown');
  const [apiKeyStatus, setApiKeyStatus] = useState<string>('checking');

  const [mapRegion, setMapRegion] = useState({
    latitude: 14.6928, // Dakar, Sénégal
    longitude: -17.4467,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });

  useEffect(() => {
    checkApiKey();
    checkLocationPermission();
  }, []);

  const checkApiKey = () => {
    const googleMapsApiKey = Constants.expoConfig?.extra?.googleMapsApiKey;
    console.log('API Key Check:', googleMapsApiKey);
    
    if (!googleMapsApiKey) {
      setApiKeyStatus('missing');
      setMapError('No Google Maps API Key found');
    } else if (googleMapsApiKey === 'AIzaSyDummy_Key_Replace_With_Real_One') {
      setApiKeyStatus('dummy');
      setMapError('Using dummy API key');
    } else {
      setApiKeyStatus('configured');
    }
  };

  const checkLocationPermission = async () => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      setPermissionStatus(status);
      
      if (status === 'granted') {
        getCurrentLocation();
      }
    } catch (error) {
      console.error('Permission check error:', error);
      setPermissionStatus('error');
    }
  };

  const getCurrentLocation = async () => {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });
      setLocation(location);
      setMapRegion({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    } catch (error) {
      console.error('Location error:', error);
    }
  };

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      setPermissionStatus(status);
      
      if (status === 'granted') {
        getCurrentLocation();
      }
    } catch (error) {
      console.error('Permission request error:', error);
    }
  };

  const handleMapReady = () => {
    console.log('Map ready!');
    setMapReady(true);
    setMapError(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'configured':
      case 'granted':
      case true:
        return colors.success[500];
      case 'missing':
      case 'denied':
      case false:
        return colors.error[500];
      default:
        return colors.warning[500];
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Map Configuration Test</Text>
      
      {/* Status Information */}
      <View style={styles.statusContainer}>
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>API Key Status:</Text>
          <Text style={[styles.statusValue, { color: getStatusColor(apiKeyStatus) }]}>
            {apiKeyStatus}
          </Text>
        </View>
        
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Location Permission:</Text>
          <Text style={[styles.statusValue, { color: getStatusColor(permissionStatus) }]}>
            {permissionStatus}
          </Text>
        </View>
        
        <View style={styles.statusItem}>
          <Text style={styles.statusLabel}>Map Ready:</Text>
          <Text style={[styles.statusValue, { color: getStatusColor(mapReady) }]}>
            {mapReady ? 'Yes' : 'No'}
          </Text>
        </View>
        
        {mapError && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Error: {mapError}</Text>
          </View>
        )}
      </View>

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={requestLocationPermission}>
          <Text style={styles.buttonText}>Request Location Permission</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={getCurrentLocation}>
          <Text style={styles.buttonText}>Get Current Location</Text>
        </TouchableOpacity>
      </View>

      {/* Map Component */}
      <View style={styles.mapContainer}>
        <Text style={styles.sectionTitle}>Map Test</Text>
        <MapView
          provider={PROVIDER_GOOGLE}
          style={styles.map}
          region={mapRegion}
          onMapReady={handleMapReady}
          showsUserLocation={true}
          showsMyLocationButton={true}
          loadingEnabled={true}
          loadingIndicatorColor={colors.primary[500]}
        >
          {location && (
            <Marker
              coordinate={{
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
              }}
              title="Your Location"
              description="Current position"
            />
          )}
        </MapView>
      </View>

      {/* Location Information */}
      {location && (
        <View style={styles.locationInfo}>
          <Text style={styles.sectionTitle}>Location Information</Text>
          <Text style={styles.infoText}>
            Latitude: {location.coords.latitude.toFixed(6)}
          </Text>
          <Text style={styles.infoText}>
            Longitude: {location.coords.longitude.toFixed(6)}
          </Text>
          <Text style={styles.infoText}>
            Accuracy: {location.coords.accuracy?.toFixed(2)}m
          </Text>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
    padding: spacing.md,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: spacing.lg,
    marginTop: spacing.xl,
  },
  statusContainer: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.lg,
  },
  statusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  statusLabel: {
    fontSize: 16,
    color: colors.text.primary,
    fontWeight: '500',
  },
  statusValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  errorContainer: {
    backgroundColor: colors.error[50],
    borderRadius: 8,
    padding: spacing.sm,
    marginTop: spacing.sm,
  },
  errorText: {
    color: colors.error[500],
    fontSize: 14,
  },
  buttonContainer: {
    marginBottom: spacing.lg,
  },
  button: {
    backgroundColor: colors.primary[500],
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.sm,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  mapContainer: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },
  map: {
    width: '100%',
    height: 300,
    borderRadius: 12,
  },
  locationInfo: {
    backgroundColor: colors.background.secondary,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.lg,
  },
  infoText: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
});

export default MapTestScreen;
