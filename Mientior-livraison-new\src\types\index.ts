// Types de base pour l'utilisateur
export interface User {
  id: string;
  email: string;
  phone: string;
  full_name: string;
  role: UserRole;
  avatar_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type UserRole = 'client' | 'livreur' | 'marchand' | 'admin';

// Types pour la géolocalisation
export interface Location {
  latitude: number;
  longitude: number;
  altitude?: number;
  accuracy?: number;
  timestamp?: number;
}

export interface Address {
  id: string;
  user_id: string;
  nom_complet: string;
  adresse_ligne1: string;
  adresse_ligne2?: string;
  ville: string;
  quartier: string;
  code_postal?: string;
  pays: string;
  instructions_livraison?: string;
  coordonnees: Location;
  is_default: boolean;
}

// Types pour les établissements
export interface merchant {
  id: string;
  nom: string;
  description?: string;
  type_merchant: 'restaurant' | 'epicerie' | 'pharmacie' | 'boutique' | 'supermarche';
  proprietaire_id: string;
  adresse: Address;
  telephone: string;
  email?: string;
  heures_ouverture: HorairesOuverture;
  image_url?: string;
  note_moyenne: number;
  nombre_avis: number;
  is_active: boolean;
  zone_livraison_km: number;
  frais_livraison_base: number;
  temps_preparation_moyen: number;
}

export interface HorairesOuverture {
  [key: string]: {
    ouvert: boolean;
    heures?: {
      ouverture: string;
      fermeture: string;
    }[];
  };
}

// Types pour les produits
export interface Produit {
  id: string;
  merchant_id: string;
  nom: string;
  description?: string;
  prix: number;
  prix_reduit?: number; // Prix promotionnel/réduit
  devise: string;
  categorie: string;
  image_url?: string;
  is_disponible: boolean;
  is_recommande?: boolean; // Produit recommandé
  temps_preparation: number;
  ingredients?: string[];
  allergenes?: string[];
  valeurs_nutritionnelles?: any;
  nombre_commandes?: number; // Nombre de commandes pour popularité
}

// Types pour les commandes
export interface Commande {
  id: string;
  client_id: string;
  merchant_id: string;
  livreur_id?: string;
  statut: StatutCommande;
  type_livraison: 'standard' | 'express' | 'programmee';
  adresse_livraison: Address;
  items: ItemCommande[];
  sous_total: number;
  frais_livraison: number;
  frais_service: number;
  taxes: number;
  total: number;
  devise: string;
  instructions_speciales?: string;
  heure_souhaitee?: string;
  temps_estime: number;
  created_at: string;
  updated_at: string;
}

export type StatutCommande =
  | 'en_attente'
  | 'confirmee'
  | 'en_preparation'
  | 'prete'
  | 'en_cours_livraison'
  | 'livree'
  | 'annulee';

export interface ItemCommande {
  id: string;
  commande_id: string;
  produit_id: string;
  produit: Produit;
  quantite: number;
  prix_unitaire: number;
  prix_total: number;
  notes?: string;
  modifications?: string[];
}

// Types pour les livraisons
export interface Livraison {
  id: string;
  commande_id: string;
  commande: Commande;
  livreur_id: string;
  livreur: User;
  statut: StatutLivraison;
  adresse_recuperation: Address;
  adresse_livraison: Address;
  distance_km: number;
  temps_estime: number;
  heure_debut?: string;
  heure_fin?: string;
  coordonnees_actuelles?: Location;
  photo_preuve?: string;
  signature_client?: string;
  code_verification?: string;
  notes_livreur?: string;
  created_at: string;
  updated_at: string;
}

export type StatutLivraison =
  | 'assignee'
  | 'acceptee'
  | 'en_route_vers_merchant'
  | 'arrivee_merchant'
  | 'commande_recuperee'
  | 'en_route_vers_client'
  | 'arrivee_client'
  | 'livree'
  | 'echec_livraison';

// Types pour les paiements
export interface Paiement {
  id: string;
  commande_id: string;
  user_id: string;
  montant: number;
  devise: string;
  methode_paiement: MethodePaiement;
  statut: StatutPaiement;
  transaction_id?: string;
  reference_externe?: string;
  numero_telephone?: string;
  details_paiement?: any;
  created_at: string;
  updated_at: string;
}

export type MethodePaiement =
  | 'especes'
  | 'orange_money'
  | 'mtn_money'
  | 'moov_money'
  | 'wave'
  | 'carte_bancaire';

export type StatutPaiement =
  | 'en_attente'
  | 'en_cours'
  | 'reussi'
  | 'echec'
  | 'rembourse';

// Types pour le panier
export interface PanierItem {
  produit: Produit;
  quantite: number;
  notes?: string;
  modifications?: string[];
}

export interface Panier {
  merchant_id: string;
  merchant?: merchant;
  items: PanierItem[];
  sous_total: number;
  devise: string;
}

// Types pour les notifications
export interface Notification {
  id: string;
  user_id: string;
  titre: string;
  contenu: string;
  type: TypeNotification;
  commande_id?: string;
  livraison_id?: string;
  is_read: boolean;
  is_lu?: boolean; // Assuming is_lu means 'is read' or 'lu' for 'lue' (read in French)
  data_json?: { [key: string]: any }; // To store additional data like orderId or deliveryId
  created_at: string;
}

export type TypeNotification =
  | 'commande_confirmee'
  | 'commande_prete'
  | 'livreur_assigne'
  | 'livraison_en_cours'
  | 'livraison_terminee'
  | 'paiement_reussi'
  | 'promotion'
  | 'systeme';

// Types pour les évaluations
export interface Evaluation {
  id: string;
  commande_id: string;
  evaluateur_id: string;
  evalué_id: string;
  type_evaluation: 'merchant' | 'livreur';
  note: number;
  commentaire?: string;
  tags?: string[];
  created_at: string;
}

// Types pour l'API
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

// Types pour les filtres et recherche
export interface FiltresRecherche {
  type_merchant?: string[];
  prix_min?: number;
  prix_max?: number;
  note_min?: number;
  distance_max?: number;
  temps_livraison_max?: number;
  livraison_gratuite?: boolean;
  ouvert_maintenant?: boolean;
}

export interface ResultatRecherche {
  merchant_profiles: merchant[];
  produits: Produit[];
  total: number;
  page: number;
  pages_total: number;
}

// Types pour les statistiques livreur
export interface StatistiquesLivreur {
  livraisons_totales: number;
  livraisons_reussies: number;
  taux_reussite: number;
  note_moyenne: number;
  revenus_total: number;
  revenus_mois: number;
  temps_moyen_livraison: number;
  distance_totale: number;
}

// Types pour les analytics commerçant
export interface AnalyticsMarchand {
  commandes_totales: number;
  commandes_mois: number;
  chiffre_affaires_total: number;
  chiffre_affaires_mois: number;
  produit_populaire: Produit;
  note_moyenne: number;
  temps_preparation_moyen: number;
  ventes_par_jour: {
    date: string;
    ventes: number;
  }[];
}

// Types pour la configuration
export interface AppConfig {
  supabaseUrl: string;
  supabaseAnonKey: string;
  googleMapsApiKey: string;
  mapboxAccessToken?: string;
}

// Types pour les erreurs de navigation
export type RootStackParamList = {
  Loading: undefined;
  Onboarding: undefined;
  LanguageSelection: undefined;
  LocationPermission: undefined;
  AuthChoiceScreen: undefined;
  SignInScreen: undefined;
  SignUpScreen: undefined;
  OTPVerificationScreen: { phone: string; email: string };
  OTPVerification: { phone: string; email: string };
  RoleSelection: undefined;
  Main: undefined;
  SupabaseTest: undefined;
};

export type ClientStackParamList = {
  Home: undefined;
  Search: { query?: string };
  Establishment: { id: string };
  Product: { id: string };
  Cart: undefined;
  Checkout: undefined;
  Orders: undefined;
  OrderTracking: { orderId: string };
  Profile: undefined;
  Addresses: undefined;
  AddAddress: { address?: Address };
  Notifications: undefined;
  Help: undefined;
};

export type DeliveryStackParamList = {
  Dashboard: undefined;
  OrderDetails: { orderId: string };
  Navigation: { deliveryId: string };
  Statistics: undefined;
  Earnings: undefined;
  Profile: undefined;
  Help: undefined;
};

export type MerchantStackParamList = {
  Dashboard: undefined;
  Orders: undefined;
  OrderDetails: { orderId: string };
  Products: undefined;
  AddProduct: { product?: Produit };
  Analytics: undefined;
  Schedule: undefined;
  Profile: undefined;
  Help: undefined;
};

// Types pour les navigateurs onglets
export type DeliveryTabParamList = {
  DeliveryHome: undefined;
  DeliveryOrders: undefined;
  DeliveryProfile: undefined;
};

export type MerchantTabParamList = {
  MerchantHome: undefined;
  MerchantOrders: undefined;
  MerchantProducts: undefined;
  MerchantProfile: undefined;
};

// Types pour les contextes
export interface AuthContextType {
  user: User | null;
  session: any;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, userData: Partial<User>) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
}

export interface LocationContextType {
  location: Location | null;
  address: string | null;
  loading: boolean;
  error: string | null;
  requestPermission: () => Promise<boolean>;
  getCurrentLocation: () => Promise<Location | null>;
  watchLocation: () => void;
  stopWatching: () => void;
}

// Types pour les fonctionnalités avancées

// Types pour le chat
export interface ChatMessage {
  id: string;
  chat_id?: string;
  delivery_id: string;
  sender_id: string;
  content: string;
  type: 'text' | 'image' | 'location' | 'system';
  metadata?: Record<string, any>;
  read_by: string[];
  created_at: string;
  updated_at: string;
  sender?: {
    id: string;
    full_name: string;
    role: string;
    avatar_url?: string;
  };
}

export interface ChatParticipant {
  id: string;
  chat_id?: string;
  delivery_id: string;
  user_id: string;
  role: 'client' | 'livreur' | 'marchand' | 'support';
  joined_at: string;
  last_read_at: string;
  user?: {
    id: string;
    full_name: string;
    role: string;
    avatar_url?: string;
    last_seen?: string;
  };
}

// Types pour le tracking avancé
export interface TrackingEvent {
  id: string;
  delivery_id: string;
  status: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  message?: string;
  timestamp: string;
}

export interface DeliveryLocation {
  latitude: number;
  longitude: number;
  address?: string;
  timestamp?: string;
}

// Types pour les analytics avancées
export interface AnalyticsEvent {
  id: string;
  user_id?: string;
  event_name: string;
  properties: Record<string, any>;
  session_id?: string;
  timestamp: string;
}

export interface AnalyticsData {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  customerSatisfaction: number;
  averageDeliveryTime: number;
  onTimeDeliveryRate: number;
  cancellationRate: number;
  periodData: Array<{
    date: string;
    orders: number;
    revenue: number;
    averageDeliveryTime: number;
    customerSatisfaction: number;
  }>;
  topProducts: Array<{
    id: string;
    name: string;
    orders: number;
    revenue: number;
    rating: number;
  }>;
  topRestaurants: Array<{
    id: string;
    name: string;
    orders: number;
    revenue: number;
    rating: number;
    averagePreparationTime: number;
  }>;
  topDeliveryPersons: Array<{
    id: string;
    name: string;
    deliveries: number;
    rating: number;
    averageDeliveryTime: number;
    earnings: number;
  }>;
}

// Types pour les tokens push
export interface PushToken {
  id: string;
  user_id: string;
  token: string;
  platform: 'ios' | 'android' | 'web';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Types pour les statistiques par rôle
export interface ClientStats {
  total: number;
  completed: number;
  averageRating: number;
  averageDeliveryTime: number;
  onTimeRate: number;
  cancellationRate: number;
  daily: number;
  weekly: number;
  monthly: number;
}

export interface DeliveryPersonStats {
  totalDeliveries: number;
  completed: number;
  cancelled: number;
  averageRating: number;
  averageTime: number;
  onTimeRate: number;
  cancellationRate: number;
  daily: number;
  weekly: number;
  monthly: number;
}

export interface MerchantStats {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  averageRating: number;
  averagePreparationTime: number;
  cancellationRate: number;
  daily: number;
  weekly: number;
  monthly: number;
}

// Types pour les préférences de notification
export interface NotificationPreferences {
  orders: boolean;
  delivery: boolean;
  promotions: boolean;
  system: boolean;
}

// Types pour les données de localisation avancées
export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy?: number;
  altitude?: number;
  heading?: number;
  speed?: number;
  timestamp?: number;
}

// Types pour les sessions analytics
export interface AnalyticsSession {
  sessionId: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  eventCount: number;
  events: AnalyticsEvent[];
}

// Types pour les métriques par période
export interface PeriodMetrics {
  date: string;
  orders: number;
  revenue: number;
  averageDeliveryTime: number;
  customerSatisfaction: number;
}

// Types pour les paramètres de navigation avancés
export type AdvancedStackParamList = {
  Notifications: undefined;
  Chat: { deliveryId: string; chatId?: string };
  Tracking: { deliveryId: string };
  Analytics: undefined;
  DeliveryTracking: { deliveryId: string };
  OrderDetails: { orderId: string };
  Promotions: undefined;
};