import { useState, useEffect, useRef } from 'react';
import { livraisonService } from '../services/supabase';
import { useLocation } from './useLocation';
import { useAuth } from './useAuth';
import { Livraison, Location } from '../types';

interface TrackingState {
  delivery: Livraison | null;
  deliveryPersonLocation: Location | null;
  estimatedArrival: Date | null;
  distance: number | null;
  duration: number | null;
  status: 'pending' | 'accepted' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled';
  loading: boolean;
  error: string | null;
  isTracking: boolean;
}

interface TrackingUpdate {
  location: Location;
  timestamp: Date;
  status: string;
  message?: string;
}

export const useTracking = (deliveryId?: string) => {
  const { user } = useAuth();
  const { location: userLocation, calculateDistance } = useLocation();
  const [state, setState] = useState<TrackingState>({
    delivery: null,
    deliveryPersonLocation: null,
    estimatedArrival: null,
    distance: null,
    duration: null,
    status: 'pending',
    loading: false,
    error: null,
    isTracking: false,
  });

  const trackingInterval = useRef<NodeJS.Timeout | null>(null);
  const lastUpdate = useRef<Date>(new Date());

  useEffect(() => {
    if (deliveryId) {
      startTracking(deliveryId);
    }

    return () => {
      stopTracking();
    };
  }, [deliveryId]);

  const startTracking = async (id: string) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null, isTracking: true }));
      
      // Charger les informations de livraison
      const delivery = await livraisonService.getById(id);
      if (!delivery) {
        throw new Error('Livraison non trouvée');
      }

      setState(prev => ({
        ...prev,
        delivery,
        status: delivery.statut as any,
        loading: false,
      }));

      // Démarrer le suivi en temps réel
      trackingInterval.current = setInterval(() => {
        updateDeliveryLocation(id);
      }, 10000); // Mise à jour toutes les 10 secondes

      // Première mise à jour immédiate
      await updateDeliveryLocation(id);

    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Erreur de suivi',
        loading: false,
        isTracking: false,
      }));
    }
  };

  const stopTracking = () => {
    if (trackingInterval.current) {
      clearInterval(trackingInterval.current);
      trackingInterval.current = null;
    }
    setState(prev => ({ ...prev, isTracking: false }));
  };

  const updateDeliveryLocation = async (id: string) => {
    try {
      // Récupérer la position actuelle du livreur
      const delivery = await livraisonService.getById(id);
      if (!delivery) return;

      // Simuler la position du livreur (en production, cela viendrait du GPS du livreur)
      const deliveryPersonLocation = delivery.position_livreur_lat && delivery.position_livreur_lng
        ? {
            latitude: delivery.position_livreur_lat,
            longitude: delivery.position_livreur_lng,
          }
        : null;

      let distance = null;
      let duration = null;
      let estimatedArrival = null;

      if (deliveryPersonLocation && userLocation) {
        // Calculer la distance
        distance = calculateDistance(deliveryPersonLocation, userLocation);
        
        // Estimer la durée (vitesse moyenne de 30 km/h en ville)
        duration = Math.round((distance / 30) * 60); // en minutes
        
        // Calculer l'heure d'arrivée estimée
        estimatedArrival = new Date(Date.now() + duration * 60 * 1000);
      }

      setState(prev => ({
        ...prev,
        delivery,
        deliveryPersonLocation,
        distance,
        duration,
        estimatedArrival,
        status: delivery.statut as any,
      }));

      lastUpdate.current = new Date();

    } catch (error) {
      console.error('Erreur lors de la mise à jour de la position:', error);
    }
  };

  const updateDeliveryStatus = async (newStatus: TrackingState['status'], message?: string) => {
    if (!state.delivery) return;

    try {
      await livraisonService.updateStatus(state.delivery.id, newStatus, message);
      
      setState(prev => ({
        ...prev,
        status: newStatus,
      }));

      // Envoyer une notification locale
      if (user?.role === 'client') {
        sendStatusNotification(newStatus, message);
      }

    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut:', error);
    }
  };

  const sendStatusNotification = (status: TrackingState['status'], message?: string) => {
    const statusMessages = {
      accepted: 'Votre commande a été acceptée par un livreur',
      picked_up: 'Votre commande a été récupérée',
      in_transit: 'Votre commande est en route',
      delivered: 'Votre commande a été livrée',
      cancelled: 'Votre livraison a été annulée',
      pending: 'Recherche d\'un livreur...',
    };

    // Cette fonction utiliserait le hook useNotifications
    console.log('Notification:', statusMessages[status], message);
  };

  const getDeliveryProgress = (): number => {
    const statusProgress = {
      pending: 0,
      accepted: 25,
      picked_up: 50,
      in_transit: 75,
      delivered: 100,
      cancelled: 0,
    };

    return statusProgress[state.status] || 0;
  };

  const getStatusMessage = (): string => {
    const messages = {
      pending: 'Recherche d\'un livreur disponible...',
      accepted: 'Livreur assigné, préparation en cours',
      picked_up: 'Commande récupérée, en route vers vous',
      in_transit: `Livraison en cours${state.duration ? `, arrivée dans ${state.duration} min` : ''}`,
      delivered: 'Commande livrée avec succès',
      cancelled: 'Livraison annulée',
    };

    return messages[state.status] || 'Statut inconnu';
  };

  const getEstimatedArrivalText = (): string => {
    if (!state.estimatedArrival) return '';
    
    const now = new Date();
    const diff = state.estimatedArrival.getTime() - now.getTime();
    const minutes = Math.round(diff / (1000 * 60));

    if (minutes <= 0) return 'Arrivée imminente';
    if (minutes === 1) return 'Arrivée dans 1 minute';
    return `Arrivée dans ${minutes} minutes`;
  };

  const getDeliveryPersonInfo = () => {
    if (!state.delivery?.livreur_id) return null;

    return {
      id: state.delivery.livreur_id,
      name: 'Livreur', // À récupérer depuis la base de données
      phone: '+225 XX XX XX XX', // À récupérer depuis la base de données
      rating: 4.8,
      vehicle: 'Moto',
    };
  };

  const callDeliveryPerson = () => {
    const deliveryPerson = getDeliveryPersonInfo();
    if (deliveryPerson?.phone) {
      // Ouvrir l'application téléphone
      console.log('Appel du livreur:', deliveryPerson.phone);
    }
  };

  const reportIssue = async (issue: string, description: string) => {
    if (!state.delivery) return;

    try {
      // Créer un rapport de problème
      console.log('Rapport de problème:', { issue, description, deliveryId: state.delivery.id });
      
      // Notifier le support
      await updateDeliveryStatus(state.status, `Problème signalé: ${issue}`);
      
    } catch (error) {
      console.error('Erreur lors du signalement:', error);
    }
  };

  const cancelDelivery = async (reason: string) => {
    if (!state.delivery) return;

    try {
      await updateDeliveryStatus('cancelled', reason);
      stopTracking();
    } catch (error) {
      console.error('Erreur lors de l\'annulation:', error);
    }
  };

  const confirmDelivery = async (rating?: number, comment?: string) => {
    if (!state.delivery) return;

    try {
      await updateDeliveryStatus('delivered');
      
      // Enregistrer l'évaluation si fournie
      if (rating) {
        console.log('Évaluation:', { rating, comment, deliveryId: state.delivery.id });
      }
      
      stopTracking();
    } catch (error) {
      console.error('Erreur lors de la confirmation:', error);
    }
  };

  const getTrackingHistory = async (): Promise<TrackingUpdate[]> => {
    if (!state.delivery) return [];

    try {
      // Récupérer l'historique des mises à jour
      // Cette fonction devrait être implémentée dans le service
      return [
        {
          location: { latitude: 0, longitude: 0 },
          timestamp: new Date(),
          status: 'pending',
          message: 'Commande créée',
        },
      ];
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique:', error);
      return [];
    }
  };

  const shareTrackingLink = () => {
    if (!state.delivery) return '';
    
    const trackingUrl = `https://mientior.app/track/${state.delivery.id}`;
    return trackingUrl;
  };

  return {
    ...state,
    startTracking,
    stopTracking,
    updateDeliveryStatus,
    getDeliveryProgress,
    getStatusMessage,
    getEstimatedArrivalText,
    getDeliveryPersonInfo,
    callDeliveryPerson,
    reportIssue,
    cancelDelivery,
    confirmDelivery,
    getTrackingHistory,
    shareTrackingLink,
    lastUpdate: lastUpdate.current,
  };
};
