import "dotenv/config";

export default {
  expo: {
    name: "Livraison Afrique",
    slug: "livraison-afrique",
    version: "1.0.0",
    sdkVersion: "53.0.0",
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "light",
    splash: {
      image: "./assets/splash.png",
      resizeMode: "contain",
      backgroundColor: "#0DCAA8"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.livraisonafrique.mobile",
      config: {
        googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY
      },
      infoPlist: {
        NSLocationWhenInUseUsageDescription: "Cette app utilise la géolocalisation pour optimiser les livraisons",
        NSLocationAlwaysAndWhenInUseUsageDescription: "Cette app utilise la géolocalisation pour optimiser les livraisons",
        NSCameraUsageDescription: "Cette app utilise l'appareil photo pour scanner les codes QR et prendre des photos de livraison",
        NSMicrophoneUsageDescription: "Cette app utilise le microphone pour les notifications vocales",
        NSPhotoLibraryUsageDescription: "Cette app accède à vos photos pour les images de profil et preuves de livraison"
      }
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#0DCAA8"
      },
      package: "com.livraisonafrique.mobile",
      permissions: [
        "ACCESS_COARSE_LOCATION",
        "ACCESS_FINE_LOCATION",
        "ACCESS_BACKGROUND_LOCATION",
        "CAMERA",
        "RECORD_AUDIO",
        "READ_EXTERNAL_STORAGE",
        "WRITE_EXTERNAL_STORAGE",
        "VIBRATE",
        "WAKE_LOCK",
        "RECEIVE_BOOT_COMPLETED"
      ],
      config: {
        googleMaps: {
          apiKey: process.env.GOOGLE_MAPS_API_KEY
        }
      }
    },
    web: {
      favicon: "./assets/favicon.png"
    },
    plugins: [
      "expo-location",
      "expo-notifications",
      "expo-camera",
      "expo-image-picker",
      [
        "expo-build-properties",
        {
          android: {
            compileSdkVersion: 34,
            targetSdkVersion: 34,
            buildToolsVersion: "34.0.0"
          },
          ios: {
            deploymentTarget: "15.1"
          }
        }
      ]
    ],
    extra: {
      eas: {
        projectId: "10d3debb-fbb4-4bb1-b4dd-eca835a67e3c"
      },
      supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL,
      supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
      googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY
    }
  }
};