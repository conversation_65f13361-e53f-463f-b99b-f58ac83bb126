const { withAndroidManifest, withInfoPlist } = require('@expo/config-plugins');

const GOOGLE_MAPS_API_KEY = 'AIzaSyBgNfqhT6lk47eh3gA0Oc9uxsB16r5lTMY';

function withGoogleMapsApiKey(config) {
  // Configure Android
  config = withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;
    const application = androidManifest.manifest.application[0];
    
    // Remove existing Google Maps API key meta-data if it exists
    if (application['meta-data']) {
      application['meta-data'] = application['meta-data'].filter(
        (meta) => meta.$['android:name'] !== 'com.google.android.geo.API_KEY'
      );
    } else {
      application['meta-data'] = [];
    }
    
    // Add the Google Maps API key
    application['meta-data'].push({
      $: {
        'android:name': 'com.google.android.geo.API_KEY',
        'android:value': GOOGLE_MAPS_API_KEY,
      },
    });
    
    return config;
  });

  // Configure iOS
  config = withInfoPlist(config, (config) => {
    config.modResults.GMSApiKey = GOOGLE_MAPS_API_KEY;
    return config;
  });

  return config;
}

module.exports = withGoogleMapsApiKey;
