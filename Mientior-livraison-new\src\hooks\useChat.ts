import { useState, useEffect, useRef } from 'react';
import { supabase } from '../services/supabase';
import { useAuth } from './useAuth';
import { useNotifications } from './useNotifications';

interface ChatMessage {
  id: string;
  senderId: string;
  senderName: string;
  senderRole: 'client' | 'livreur' | 'marchand' | 'support';
  message: string;
  timestamp: Date;
  type: 'text' | 'image' | 'location' | 'system';
  metadata?: {
    imageUrl?: string;
    location?: {
      latitude: number;
      longitude: number;
      address?: string;
    };
    systemType?: 'status_update' | 'delivery_info' | 'order_update';
  };
  read: boolean;
  delivered: boolean;
}

interface ChatState {
  messages: ChatMessage[];
  participants: ChatParticipant[];
  loading: boolean;
  error: string | null;
  isConnected: boolean;
  isTyping: boolean;
  typingUsers: string[];
  unreadCount: number;
}

interface ChatParticipant {
  id: string;
  name: string;
  role: 'client' | 'livreur' | 'marchand' | 'support';
  avatar?: string;
  isOnline: boolean;
  lastSeen?: Date;
}

export const useChat = (chatId?: string, deliveryId?: string) => {
  const { user } = useAuth();
  const { sendLocalNotification } = useNotifications();
  const [state, setState] = useState<ChatState>({
    messages: [],
    participants: [],
    loading: false,
    error: null,
    isConnected: false,
    isTyping: false,
    typingUsers: [],
    unreadCount: 0,
  });

  const subscription = useRef<any>(null);
  const typingTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (chatId || deliveryId) {
      initializeChat();
    }

    return () => {
      cleanup();
    };
  }, [chatId, deliveryId]);

  const initializeChat = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // Charger les messages existants
      await loadMessages();
      
      // Charger les participants
      await loadParticipants();

      // S'abonner aux nouveaux messages en temps réel
      subscribeToMessages();

      // S'abonner aux indicateurs de frappe
      subscribeToTyping();

      setState(prev => ({ ...prev, loading: false, isConnected: true }));

    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Erreur de connexion au chat',
        loading: false,
      }));
    }
  };

  const cleanup = () => {
    if (subscription.current) {
      subscription.current.unsubscribe();
    }
    if (typingTimeout.current) {
      clearTimeout(typingTimeout.current);
    }
    setState(prev => ({ ...prev, isConnected: false }));
  };

  const loadMessages = async () => {
    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .select(`
          *,
          sender:profiles(id, full_name, role, avatar_url)
        `)
        .eq(chatId ? 'chat_id' : 'delivery_id', chatId || deliveryId)
        .order('created_at', { ascending: true });

      if (error) throw error;

      const messages: ChatMessage[] = data.map(msg => ({
        id: msg.id,
        senderId: msg.sender_id,
        senderName: msg.sender?.full_name || 'Utilisateur',
        senderRole: msg.sender?.role || 'client',
        message: msg.content,
        timestamp: new Date(msg.created_at),
        type: msg.type || 'text',
        metadata: msg.metadata,
        read: msg.read_by?.includes(user?.id) || false,
        delivered: true,
      }));

      const unreadCount = messages.filter(m => 
        m.senderId !== user?.id && !m.read
      ).length;

      setState(prev => ({
        ...prev,
        messages,
        unreadCount,
      }));

    } catch (error) {
      console.error('Erreur lors du chargement des messages:', error);
    }
  };

  const loadParticipants = async () => {
    try {
      // Charger les participants du chat
      const { data, error } = await supabase
        .from('chat_participants')
        .select(`
          *,
          profile:profiles(id, full_name, role, avatar_url, last_seen)
        `)
        .eq(chatId ? 'chat_id' : 'delivery_id', chatId || deliveryId);

      if (error) throw error;

      const participants: ChatParticipant[] = data.map(p => ({
        id: p.profile.id,
        name: p.profile.full_name || 'Utilisateur',
        role: p.profile.role,
        avatar: p.profile.avatar_url,
        isOnline: p.profile.last_seen ? 
          (Date.now() - new Date(p.profile.last_seen).getTime()) < 5 * 60 * 1000 : false,
        lastSeen: p.profile.last_seen ? new Date(p.profile.last_seen) : undefined,
      }));

      setState(prev => ({ ...prev, participants }));

    } catch (error) {
      console.error('Erreur lors du chargement des participants:', error);
    }
  };

  const subscribeToMessages = () => {
    subscription.current = supabase
      .channel(`chat_${chatId || deliveryId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: chatId ? `chat_id=eq.${chatId}` : `delivery_id=eq.${deliveryId}`,
        },
        (payload) => {
          handleNewMessage(payload.new);
        }
      )
      .subscribe();
  };

  const subscribeToTyping = () => {
    supabase
      .channel(`typing_${chatId || deliveryId}`)
      .on('broadcast', { event: 'typing' }, (payload) => {
        handleTypingIndicator(payload);
      })
      .subscribe();
  };

  const handleNewMessage = async (messageData: any) => {
    try {
      // Récupérer les informations du sender
      const { data: senderData } = await supabase
        .from('profiles')
        .select('full_name, role, avatar_url')
        .eq('id', messageData.sender_id)
        .single();

      const newMessage: ChatMessage = {
        id: messageData.id,
        senderId: messageData.sender_id,
        senderName: senderData?.full_name || 'Utilisateur',
        senderRole: senderData?.role || 'client',
        message: messageData.content,
        timestamp: new Date(messageData.created_at),
        type: messageData.type || 'text',
        metadata: messageData.metadata,
        read: false,
        delivered: true,
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, newMessage],
        unreadCount: newMessage.senderId !== user?.id ? prev.unreadCount + 1 : prev.unreadCount,
      }));

      // Envoyer une notification si le message ne vient pas de l'utilisateur actuel
      if (newMessage.senderId !== user?.id) {
        sendLocalNotification(
          `Nouveau message de ${newMessage.senderName}`,
          newMessage.message,
          { type: 'chat', chatId: chatId || deliveryId }
        );
      }

    } catch (error) {
      console.error('Erreur lors du traitement du nouveau message:', error);
    }
  };

  const handleTypingIndicator = (payload: any) => {
    const { userId, isTyping } = payload.payload;
    
    if (userId === user?.id) return; // Ignorer ses propres indicateurs

    setState(prev => {
      let newTypingUsers = [...prev.typingUsers];
      
      if (isTyping && !newTypingUsers.includes(userId)) {
        newTypingUsers.push(userId);
      } else if (!isTyping) {
        newTypingUsers = newTypingUsers.filter(id => id !== userId);
      }

      return {
        ...prev,
        typingUsers: newTypingUsers,
      };
    });
  };

  const sendMessage = async (content: string, type: 'text' | 'image' | 'location' | 'system' = 'text', metadata?: any) => {
    if (!user || !content.trim()) return;

    try {
      const messageData = {
        chat_id: chatId,
        delivery_id: deliveryId,
        sender_id: user.id,
        content: content.trim(),
        type,
        metadata,
        created_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('chat_messages')
        .insert(messageData)
        .select()
        .single();

      if (error) throw error;

      // Le message sera ajouté via la subscription temps réel
      return data;

    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error);
      throw error;
    }
  };

  const sendImage = async (imageUri: string, caption?: string) => {
    try {
      // Upload de l'image vers Supabase Storage
      const fileName = `chat_images/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`;
      
      // Cette partie nécessiterait l'implémentation de l'upload d'image
      const imageUrl = imageUri; // Temporaire
      
      await sendMessage(caption || 'Image', 'image', { imageUrl });

    } catch (error) {
      console.error('Erreur lors de l\'envoi de l\'image:', error);
      throw error;
    }
  };

  const sendLocation = async (latitude: number, longitude: number, address?: string) => {
    try {
      await sendMessage(
        address || `Position: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
        'location',
        { location: { latitude, longitude, address } }
      );
    } catch (error) {
      console.error('Erreur lors de l\'envoi de la position:', error);
      throw error;
    }
  };

  const sendSystemMessage = async (message: string, systemType: string) => {
    try {
      await sendMessage(message, 'system', { systemType });
    } catch (error) {
      console.error('Erreur lors de l\'envoi du message système:', error);
    }
  };

  const markMessagesAsRead = async () => {
    if (!user) return;

    try {
      const unreadMessages = state.messages.filter(m => 
        m.senderId !== user.id && !m.read
      );

      if (unreadMessages.length === 0) return;

      const messageIds = unreadMessages.map(m => m.id);

      // Update each message individually to mark as read
      const updatePromises = messageIds.map(messageId =>
        supabase.rpc('mark_message_as_read', {
          message_id: messageId,
          user_id: user.id
        })
      );

      const results = await Promise.all(updatePromises);
      const hasError = results.some(result => result.error);

      if (hasError) {
        throw new Error('Erreur lors du marquage des messages comme lus');
      }

      setState(prev => ({
        ...prev,
        messages: prev.messages.map(m => 
          messageIds.includes(m.id) ? { ...m, read: true } : m
        ),
        unreadCount: 0,
      }));

    } catch (error) {
      console.error('Erreur lors du marquage des messages comme lus:', error);
    }
  };

  const setTyping = async (isTyping: boolean) => {
    if (!user) return;

    try {
      await supabase.channel(`typing_${chatId || deliveryId}`)
        .send({
          type: 'broadcast',
          event: 'typing',
          payload: { userId: user.id, isTyping }
        });

      setState(prev => ({ ...prev, isTyping }));

      // Arrêter automatiquement l'indicateur après 3 secondes
      if (isTyping) {
        if (typingTimeout.current) {
          clearTimeout(typingTimeout.current);
        }
        typingTimeout.current = setTimeout(() => {
          setTyping(false);
        }, 3000);
      }

    } catch (error) {
      console.error('Erreur lors de l\'envoi de l\'indicateur de frappe:', error);
    }
  };

  const getTypingText = (): string => {
    if (state.typingUsers.length === 0) return '';
    
    const typingNames = state.typingUsers
      .map(userId => {
        const participant = state.participants.find(p => p.id === userId);
        return participant?.name || 'Quelqu\'un';
      })
      .slice(0, 2); // Limiter à 2 noms

    if (typingNames.length === 1) {
      return `${typingNames[0]} est en train d'écrire...`;
    } else if (typingNames.length === 2) {
      return `${typingNames[0]} et ${typingNames[1]} sont en train d'écrire...`;
    } else {
      return 'Plusieurs personnes sont en train d\'écrire...';
    }
  };

  const deleteMessage = async (messageId: string) => {
    try {
      const { error } = await supabase
        .from('chat_messages')
        .delete()
        .eq('id', messageId)
        .eq('sender_id', user?.id); // Seul l'expéditeur peut supprimer

      if (error) throw error;

      setState(prev => ({
        ...prev,
        messages: prev.messages.filter(m => m.id !== messageId),
      }));

    } catch (error) {
      console.error('Erreur lors de la suppression du message:', error);
      throw error;
    }
  };

  const blockUser = async (userId: string) => {
    try {
      // Implémenter la logique de blocage
      console.log('Blocage de l\'utilisateur:', userId);
    } catch (error) {
      console.error('Erreur lors du blocage:', error);
    }
  };

  const reportMessage = async (messageId: string, reason: string) => {
    try {
      // Implémenter la logique de signalement
      console.log('Signalement du message:', messageId, reason);
    } catch (error) {
      console.error('Erreur lors du signalement:', error);
    }
  };

  return {
    ...state,
    sendMessage,
    sendImage,
    sendLocation,
    sendSystemMessage,
    markMessagesAsRead,
    setTyping,
    getTypingText,
    deleteMessage,
    blockUser,
    reportMessage,
    refresh: loadMessages,
  };
};
